import type { Core } from "@strapi/strapi";

interface AnalyticsController {
    getAnalytics(ctx: any): Promise<any>;
    getResults(ctx: any): Promise<any>;
}

export default {
    async getAnalytics(ctx) {
        const { id: documentId } = ctx.params;
        const strapi = ctx.state.strapi as Core.Strapi;

        try {
            // Get poll with relations using documentId
            const poll = await strapi.db.query("api::poll.poll").findOne({
                where: { documentId },
                populate: ["geographicZone", "questions.options", "organization"],
            });

            if (!poll) {
                return ctx.notFound("Poll not found");
            }

            // Check cache first using numeric id
            const analyticsCache = strapi.service("api::poll.poll-analytics-cache");
            const cachedData = await analyticsCache.getCachedAnalytics(poll.id);

            if (cachedData) {
                return cachedData;
            }

            // Check permissions - only poll creator or organization admin can view analytics
            const userId = ctx.state.user?.id;
            if (!userId) {
                return ctx.unauthorized("Authentication required");
            }

            // Get all participations for this poll using numeric id
            const participations = await strapi.db
                .query("api::participation.participation")
                .findMany({
                    where: {
                        poll: poll.id,
                        completedAt: { $notNull: true },
                    },
                    populate: ["user.userLocation.geographicZone"],
                });

            // Calculate basic statistics
            const totalPopulation = poll.geographicZone.population || 100000;
            const totalParticipants = participations.length;
            const participationRate = (totalParticipants / totalPopulation) * 100;

            // Calculate average completion time
            const completionTimes = participations
                .filter((p) => p.startedAt && p.completedAt)
                .map((p) => {
                    const start = new Date(p.startedAt).getTime();
                    const end = new Date(p.completedAt).getTime();
                    return (end - start) / 1000 / 60; // minutes
                });

            const averageCompletionTime =
                completionTimes.length > 0
                    ? Math.round(
                          completionTimes.reduce((a, b) => a + b, 0) / completionTimes.length,
                      )
                    : 0;

            // Get responses grouped by question
            const responses = await strapi.db.query("api::response.response").findMany({
                where: {
                    participation: {
                        poll: poll.id,
                    },
                },
                populate: ["question.options", "selectedOptions"],
            });

            // Process responses by question
            const questionStats: any = {};
            for (const question of poll.questions) {
                const questionResponses = responses.filter((r) => r.question.id === question.id);

                switch (question.type) {
                    case "single_choice":
                    case "multiple_choice":
                        const optionCounts: Record<string, number> = {};
                        question.options.forEach((opt: any) => {
                            optionCounts[opt.id] = 0;
                        });

                        questionResponses.forEach((r) => {
                            r.selectedOptions?.forEach((opt: any) => {
                                if (optionCounts[opt.id] !== undefined) {
                                    optionCounts[opt.id]++;
                                }
                            });
                        });

                        questionStats[question.id] = {
                            questionDocumentId: question.documentId,
                            questionText: question.text,
                            type: question.type,
                            data: question.options.map((opt: any) => ({
                                label: opt.text,
                                value: opt.id,
                                count: optionCounts[opt.id],
                                percentage:
                                    totalParticipants > 0
                                        ? (
                                              (optionCounts[opt.id] / totalParticipants) *
                                              100
                                          ).toFixed(1)
                                        : 0,
                            })),
                        };
                        break;

                    case "rating":
                        const ratings: number[] = questionResponses
                            .map((r) => parseInt(r.ratingValue))
                            .filter((v) => !isNaN(v));

                        const ratingCounts: Record<number, number> = {};
                        for (let i = 1; i <= 5; i++) {
                            ratingCounts[i] = ratings.filter((r) => r === i).length;
                        }

                        const averageRating =
                            ratings.length > 0
                                ? (ratings.reduce((a, b) => a + b, 0) / ratings.length).toFixed(1)
                                : 0;

                        questionStats[question.id] = {
                            questionDocumentId: question.documentId,
                            questionText: question.text,
                            type: question.type,
                            averageRating,
                            data: Object.entries(ratingCounts).map(([rating, count]) => ({
                                rating: parseInt(rating),
                                count,
                                percentage:
                                    totalParticipants > 0
                                        ? ((count / totalParticipants) * 100).toFixed(1)
                                        : 0,
                            })),
                        };
                        break;

                    case "text":
                        // For text responses, we'll just count responses
                        questionStats[question.id] = {
                            questionDocumentId: question.documentId,
                            questionText: question.text,
                            type: question.type,
                            responseCount: questionResponses.filter(
                                (r) => r.textValue && r.textValue.trim(),
                            ).length,
                            totalParticipants,
                        };
                        break;

                    case "ranking":
                        // Process ranking data
                        const rankingData: any = {};
                        question.options.forEach((opt: any) => {
                            rankingData[opt.id] = {
                                optionText: opt.text,
                                rankings: [],
                            };
                        });

                        questionResponses.forEach((r) => {
                            if (r.rankingValue) {
                                try {
                                    const rankings = JSON.parse(r.rankingValue);
                                    Object.entries(rankings).forEach(([optionId, rank]) => {
                                        if (rankingData[optionId]) {
                                            rankingData[optionId].rankings.push(rank as number);
                                        }
                                    });
                                } catch (e) {
                                    // Invalid JSON, skip
                                }
                            }
                        });

                        // Calculate average rank for each option
                        const rankingStats = Object.entries(rankingData)
                            .map(([optionId, data]: any) => ({
                                optionId,
                                optionText: data.optionText,
                                averageRank:
                                    data.rankings.length > 0
                                        ? (
                                              data.rankings.reduce(
                                                  (a: number, b: number) => a + b,
                                                  0,
                                              ) / data.rankings.length
                                          ).toFixed(1)
                                        : "0",
                                responseCount: data.rankings.length,
                            }))
                            .sort((a, b) => parseFloat(a.averageRank) - parseFloat(b.averageRank));

                        questionStats[question.id] = {
                            questionDocumentId: question.documentId,
                            questionText: question.text,
                            type: question.type,
                            data: rankingStats,
                        };
                        break;
                }
            }

            // Geographic distribution
            const geoDistribution: Record<string, any> = {};
            participations.forEach((p) => {
                if (p.user?.userLocation?.geographicZone) {
                    const zone = p.user.userLocation.geographicZone;
                    if (!geoDistribution[zone.id]) {
                        geoDistribution[zone.id] = {
                            zoneId: zone.id,
                            zoneName: zone.name,
                            zoneType: zone.type,
                            participants: 0,
                        };
                    }
                    geoDistribution[zone.id].participants++;
                }
            });

            // Temporal data - participation over time
            const temporalData: Record<string, number> = {};
            participations.forEach((p) => {
                const date = new Date(p.completedAt).toISOString().split("T")[0];
                temporalData[date] = (temporalData[date] || 0) + 1;
            });

            // Sort temporal data by date
            const sortedTemporalData = Object.entries(temporalData)
                .sort(([a], [b]) => a.localeCompare(b))
                .map(([date, count]) => ({ date, dailyParticipations: count }));

            // Prepare analytics data
            const analyticsData = {
                poll: {
                    documentId: poll.documentId,
                    title: poll.title,
                    startDate: poll.startDate,
                    endDate: poll.endDate,
                    status: poll.pollStatus,
                    organization: poll.organization?.name,
                },
                statistics: {
                    totalParticipants,
                    targetPopulation: totalPopulation,
                    participationRate: participationRate.toFixed(2),
                    averageCompletionTime,
                    completionRate: ((totalParticipants / participations.length) * 100).toFixed(2),
                },
                responses: Object.values(questionStats),
                geographic: {
                    distribution: Object.values(geoDistribution).sort(
                        (a: any, b: any) => b.participants - a.participants,
                    ),
                    mainZone: poll.geographicZone.name,
                    zoneType: poll.geographicZone.type,
                },
                temporal: sortedTemporalData,
            };

            // Cache the results
            await analyticsCache.setCachedAnalytics(poll.id, analyticsData);

            return analyticsData;
        } catch (error) {
            strapi.log.error("Error fetching poll analytics:", error);
            return ctx.internalServerError("Failed to fetch analytics");
        }
    },

    async getResults(ctx) {
        const { id: documentId } = ctx.params;
        const strapi = ctx.state.strapi as Core.Strapi;

        try {
            // Get poll using documentId
            const poll = await strapi.db.query("api::poll.poll").findOne({
                where: { documentId },
                populate: ["questions.options"],
            });

            if (!poll) {
                return ctx.notFound("Poll not found");
            }

            // Check cache first using numeric id
            const analyticsCache = strapi.service("api::poll.poll-analytics-cache");
            const cachedData = await analyticsCache.getCachedResults(poll.id);

            if (cachedData) {
                return cachedData;
            }

            // Check if poll is closed or user has participated
            const now = new Date();
            const pollClosed = new Date(poll.endDate) < now;
            let userParticipated = false;

            if (ctx.state.user?.id) {
                const participation = await strapi.db
                    .query("api::participation.participation")
                    .findOne({
                        where: {
                            poll: poll.id,
                            user: ctx.state.user.id,
                            completedAt: { $notNull: true },
                        },
                    });
                userParticipated = !!participation;
            }

            // Only show results if poll is closed or user has participated
            if (!pollClosed && !userParticipated) {
                return ctx.forbidden("Results are not available yet");
            }

            // Get total participants
            const totalParticipants = await strapi.db
                .query("api::participation.participation")
                .count({
                    where: {
                        poll: poll.id,
                        completedAt: { $notNull: true },
                    },
                });

            // Get aggregated results by question
            const results: any[] = [];

            for (const question of poll.questions) {
                const questionResult: any = {
                    questionDocumentId: question.documentId,
                    questionText: question.text,
                    type: question.type,
                    totalResponses: 0,
                };

                switch (question.type) {
                    case "single_choice":
                    case "multiple_choice":
                        // Get response counts for each option
                        const optionResults = await Promise.all(
                            question.options.map(async (option: any) => {
                                const count = await strapi.db
                                    .query("api::response.response")
                                    .count({
                                        where: {
                                            question: question.id,
                                            selectedOptions: option.id,
                                        },
                                    });
                                return {
                                    optionId: option.id,
                                    optionText: option.text,
                                    count,
                                    percentage:
                                        totalParticipants > 0
                                            ? ((count / totalParticipants) * 100).toFixed(1)
                                            : "0",
                                };
                            }),
                        );

                        questionResult.options = optionResults;
                        questionResult.totalResponses = optionResults.reduce(
                            (sum, opt) => sum + opt.count,
                            0,
                        );
                        break;

                    case "rating":
                        // Get rating distribution
                        const ratings = await strapi.db.query("api::response.response").findMany({
                            where: {
                                question: question.id,
                                ratingValue: { $notNull: true },
                            },
                            select: ["ratingValue"],
                        });

                        const ratingDistribution: Record<number, number> = {
                            1: 0,
                            2: 0,
                            3: 0,
                            4: 0,
                            5: 0,
                        };
                        let totalRating = 0;

                        ratings.forEach((r) => {
                            const value = parseInt(r.ratingValue);
                            if (value >= 1 && value <= 5) {
                                ratingDistribution[value]++;
                                totalRating += value;
                            }
                        });

                        questionResult.averageRating =
                            ratings.length > 0 ? (totalRating / ratings.length).toFixed(1) : "0";
                        questionResult.distribution = Object.entries(ratingDistribution).map(
                            ([rating, count]) => ({
                                rating: parseInt(rating),
                                count,
                                percentage:
                                    totalParticipants > 0
                                        ? ((count / totalParticipants) * 100).toFixed(1)
                                        : "0",
                            }),
                        );
                        questionResult.totalResponses = ratings.length;
                        break;

                    case "text":
                        // Just count text responses (don't expose actual text for privacy)
                        const textResponses = await strapi.db
                            .query("api::response.response")
                            .count({
                                where: {
                                    question: question.id,
                                    textValue: { $notNull: true },
                                },
                            });
                        questionResult.totalResponses = textResponses;
                        break;

                    case "ranking":
                        // Get average rankings
                        const rankingResponses = await strapi.db
                            .query("api::response.response")
                            .findMany({
                                where: {
                                    question: question.id,
                                    rankingValue: { $notNull: true },
                                },
                                select: ["rankingValue"],
                            });

                        const rankingSums: Record<string, { sum: number; count: number }> = {};
                        question.options.forEach((opt: any) => {
                            rankingSums[opt.id] = { sum: 0, count: 0 };
                        });

                        rankingResponses.forEach((r) => {
                            try {
                                const rankings = JSON.parse(r.rankingValue);
                                Object.entries(rankings).forEach(([optionId, rank]) => {
                                    if (rankingSums[optionId]) {
                                        rankingSums[optionId].sum += rank as number;
                                        rankingSums[optionId].count++;
                                    }
                                });
                            } catch (e) {
                                // Invalid JSON, skip
                            }
                        });

                        questionResult.rankings = question.options
                            .map((opt: any) => ({
                                optionId: opt.id,
                                optionText: opt.text,
                                averageRank:
                                    rankingSums[opt.id].count > 0
                                        ? (
                                              rankingSums[opt.id].sum / rankingSums[opt.id].count
                                          ).toFixed(1)
                                        : "0",
                            }))
                            .sort(
                                (a: any, b: any) =>
                                    parseFloat(a.averageRank) - parseFloat(b.averageRank),
                            );
                        questionResult.totalResponses = rankingResponses.length;
                        break;
                }

                results.push(questionResult);
            }

            const resultsData = {
                poll: {
                    documentId: poll.documentId,
                    title: poll.title,
                    description: poll.description,
                    endDate: poll.endDate,
                    status: pollClosed ? "closed" : poll.pollStatus,
                },
                totalParticipants,
                results,
            };

            // Cache the results
            await analyticsCache.setCachedResults(poll.id, resultsData);

            return resultsData;
        } catch (error) {
            strapi.log.error("Error fetching poll results:", error);
            return ctx.internalServerError("Failed to fetch results");
        }
    },
} as AnalyticsController;

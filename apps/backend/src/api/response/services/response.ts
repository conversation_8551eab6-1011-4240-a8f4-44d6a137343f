/**
 * response service
 */

import { factories } from "@strapi/strapi";
import type { Core } from "@strapi/strapi";

export default factories.createCoreService(
    "api::response.response",
    ({ strapi }: { strapi: Core.Strapi }) => ({
        /**
         * Helper method to resolve option IDs from documentIds
         */
        async resolveOptionIds(optionDocumentIds: string[], questionId: number): Promise<number[]> {
            if (!Array.isArray(optionDocumentIds) || optionDocumentIds.length === 0) {
                return [];
            }

            const options = await strapi.db.query("api::option.option").findMany({
                where: {
                    documentId: { $in: optionDocumentIds },
                    question: questionId,
                },
                select: ["id", "documentId"],
            });

            // Validate that all provided documentIds exist and belong to the question
            const foundDocumentIds = options.map((opt) => opt.documentId);
            const missingDocumentIds = optionDocumentIds.filter(
                (docId) => !foundDocumentIds.includes(docId),
            );

            if (missingDocumentIds.length > 0) {
                throw new Error(
                    `Options non trouvées pour la question ${questionId}: ${missingDocumentIds.join(", ")}`,
                );
            }

            return options.map((opt) => opt.id);
        },

        /**
         * Custom method to create a response with proper relation handling
         */
        async createResponseForParticipation(data: {
            participationId: number;
            questionId: number;
            pollId: number;
            responseValue: any;
            questionType: string;
        }) {
            try {
                const { participationId, questionId, pollId, responseValue, questionType } = data;

                // Base response data
                const responseData: any = {
                    answeredAt: new Date().toISOString(),
                };

                let selectedOptionIds: number[] = [];

                // Handle different response types
                switch (questionType) {
                    case "single_choice":
                        if (typeof responseValue === "string" && responseValue.trim() !== "") {
                            selectedOptionIds = await this.resolveOptionIds(
                                [responseValue],
                                questionId,
                            );
                        } else if (responseValue !== null && responseValue !== undefined) {
                            throw new Error(
                                `Réponse invalide pour une question à choix unique: ${responseValue}`,
                            );
                        }
                        break;

                    case "multiple_choice":
                        if (Array.isArray(responseValue)) {
                            // Filter out empty values
                            const validValues = responseValue.filter(
                                (val) => val && typeof val === "string" && val.trim() !== "",
                            );
                            if (validValues.length > 0) {
                                selectedOptionIds = await this.resolveOptionIds(
                                    validValues,
                                    questionId,
                                );
                            }
                        } else if (responseValue !== null && responseValue !== undefined) {
                            throw new Error(
                                `Réponse invalide pour une question à choix multiple: ${responseValue}`,
                            );
                        }
                        break;

                    case "rating":
                        if (
                            typeof responseValue === "number" ||
                            typeof responseValue === "string"
                        ) {
                            const ratingValue = parseInt(responseValue.toString());
                            if (isNaN(ratingValue) || ratingValue < 1 || ratingValue > 10) {
                                throw new Error(
                                    `Valeur de notation invalide: ${responseValue}. Doit être entre 1 et 10.`,
                                );
                            }
                            responseData.ratingValue = ratingValue;
                        } else if (responseValue !== null && responseValue !== undefined) {
                            throw new Error(
                                `Réponse invalide pour une question de notation: ${responseValue}`,
                            );
                        }
                        break;

                    case "text":
                        if (typeof responseValue === "string") {
                            const trimmedValue = responseValue.trim();
                            if (trimmedValue.length > 500) {
                                throw new Error(
                                    `Réponse textuelle trop longue. Maximum 500 caractères.`,
                                );
                            }
                            responseData.textValue = trimmedValue;
                        } else if (responseValue !== null && responseValue !== undefined) {
                            throw new Error(
                                `Réponse invalide pour une question textuelle: ${responseValue}`,
                            );
                        }
                        break;

                    case "ranking":
                        if (Array.isArray(responseValue)) {
                            // Validate ranking format: [{optionId: string, rank: number}]
                            const isValidRanking = responseValue.every(
                                (item) =>
                                    item &&
                                    typeof item === "object" &&
                                    typeof item.optionId === "string" &&
                                    typeof item.rank === "number" &&
                                    item.rank > 0,
                            );

                            if (isValidRanking) {
                                responseData.rankingOrder = responseValue;
                            } else {
                                throw new Error(
                                    `Format de classement invalide. Attendu: [{optionId: string, rank: number}]`,
                                );
                            }
                        } else if (responseValue !== null && responseValue !== undefined) {
                            throw new Error(
                                `Réponse invalide pour une question de classement: ${responseValue}`,
                            );
                        }
                        break;
                }

                // Create response using entity service for proper relation handling
                const createdResponse = await strapi.entityService.create(
                    "api::response.response",
                    {
                        data: {
                            ...responseData,
                            participation: participationId,
                            question: questionId,
                            poll: pollId,
                            selectedOptions:
                                selectedOptionIds.length > 0 ? selectedOptionIds : undefined,
                        },
                    },
                );

                strapi.log.info(
                    `Response created successfully for question ${questionId}:`,
                    createdResponse.id,
                );
                return createdResponse;
            } catch (error: any) {
                strapi.log.error("Error in createResponseForParticipation:", error);
                throw new Error(`Échec de la création de la réponse: ${error.message}`);
            }
        },

        /**
         * Create multiple responses for a participation
         */
        async createResponsesForParticipation(
            participationId: number,
            pollId: number,
            responses: Record<string, any>,
            questions: any[],
        ) {
            const createdResponses = [];

            for (const [questionDocumentId, responseValue] of Object.entries(responses)) {
                try {
                    // Find question by documentId
                    const question = questions.find(
                        (q: any) => q.documentId === questionDocumentId,
                    );

                    if (!question || responseValue === null || responseValue === undefined) {
                        continue;
                    }

                    const response = await this.createResponseForParticipation({
                        participationId,
                        questionId: question.id,
                        pollId,
                        responseValue,
                        questionType: question.type,
                    });

                    createdResponses.push(response);
                } catch (error: any) {
                    strapi.log.error(
                        `Failed to create response for question ${questionDocumentId}:`,
                        error,
                    );
                    // Continue with other responses
                }
            }

            return createdResponses;
        },

        /**
         * Migrate legacy textValue responses to selectedOptions relations
         * This should be run once after deploying the new response handling code
         */
        async migrateLegacyResponses(strapi: Core.Strapi) {
            try {
                strapi.log.info("Starting migration of legacy responses...");

                // Find all responses with textValue that start with "option:" or "options:"
                const legacyResponses = await strapi.db.query("api::response.response").findMany({
                    where: {
                        textValue: { $notNull: true },
                        selectedOptions: { $null: true }, // Only migrate if selectedOptions is not set
                        $or: [
                            { textValue: { $startsWith: "option:" } },
                            { textValue: { $startsWith: "options:" } },
                        ],
                    },
                    populate: {
                        question: {
                            populate: ["options"],
                        },
                    },
                });

                strapi.log.info(`Found ${legacyResponses.length} legacy responses to migrate`);

                let migratedCount = 0;
                let errorCount = 0;

                for (const response of legacyResponses) {
                    try {
                        const question = response.question;
                        if (!question || !question.options) {
                            strapi.log.warn(
                                `Skipping response ${response.id}: question or options not found`,
                            );
                            continue;
                        }

                        let optionIds: number[] = [];

                        if (response.textValue.startsWith("option:")) {
                            // Single choice: "option:documentId"
                            const optionDocumentId = response.textValue.substring(7);
                            const option = question.options.find(
                                (opt: any) => opt.documentId === optionDocumentId,
                            );
                            if (option) {
                                optionIds = [option.id];
                            } else {
                                strapi.log.warn(
                                    `Option with documentId ${optionDocumentId} not found for response ${response.id}`,
                                );
                                continue;
                            }
                        } else if (response.textValue.startsWith("options:")) {
                            // Multiple choice: "options:[\"documentId1\",\"documentId2\"]"
                            try {
                                const optionDocumentIds = JSON.parse(
                                    response.textValue.substring(8),
                                );
                                if (Array.isArray(optionDocumentIds)) {
                                    optionIds = [];
                                    for (const docId of optionDocumentIds) {
                                        const option = question.options.find(
                                            (opt: any) => opt.documentId === docId,
                                        );
                                        if (option) {
                                            optionIds.push(option.id);
                                        } else {
                                            strapi.log.warn(
                                                `Option with documentId ${docId} not found for response ${response.id}`,
                                            );
                                        }
                                    }
                                }
                            } catch (parseError) {
                                strapi.log.warn(
                                    `Failed to parse options JSON for response ${response.id}: ${response.textValue}`,
                                );
                                continue;
                            }
                        }

                        if (optionIds.length > 0) {
                            // Use Strapi v5 relation API to set selectedOptions
                            await strapi.db.query("api::response.response").update({
                                where: { id: response.id },
                                data: {
                                    textValue: null, // Clear the legacy textValue
                                    selectedOptions: {
                                        connect: optionIds.map((id) => ({ id })),
                                    },
                                },
                            });

                            migratedCount++;
                            strapi.log.debug(
                                `Migrated response ${response.id} with ${optionIds.length} options`,
                            );
                        }
                    } catch (error: any) {
                        errorCount++;
                        strapi.log.error(`Error migrating response ${response.id}:`, error);
                    }
                }

                strapi.log.info(
                    `Migration completed: ${migratedCount} responses migrated, ${errorCount} errors`,
                );

                return {
                    total: legacyResponses.length,
                    migrated: migratedCount,
                    errors: errorCount,
                };
            } catch (error: any) {
                strapi.log.error("Migration failed:", error);
                throw error;
            }
        },

        /**
         * Rollback migration - convert selectedOptions back to textValue format
         * Use this if you need to rollback the migration
         */
        async rollbackMigration(strapi: Core.Strapi) {
            try {
                strapi.log.info("Starting rollback of migrated responses...");

                // Find responses with selectedOptions but no textValue
                const migratedResponses = await strapi.db.query("api::response.response").findMany({
                    where: {
                        selectedOptions: { $notNull: true },
                        textValue: { $null: true },
                    },
                    populate: {
                        selectedOptions: true,
                        question: true,
                    },
                });

                strapi.log.info(`Found ${migratedResponses.length} migrated responses to rollback`);

                let rolledBackCount = 0;
                let errorCount = 0;

                for (const response of migratedResponses) {
                    try {
                        const question = response.question;
                        const selectedOptions = response.selectedOptions;

                        if (!question || !selectedOptions || selectedOptions.length === 0) {
                            continue;
                        }

                        let textValue: string;

                        if (question.type === "single_choice" && selectedOptions.length === 1) {
                            textValue = `option:${selectedOptions[0].documentId}`;
                        } else if (question.type === "multiple_choice") {
                            const documentIds = selectedOptions.map((opt: any) => opt.documentId);
                            textValue = `options:${JSON.stringify(documentIds)}`;
                        } else {
                            // Skip responses that don't match expected patterns
                            continue;
                        }

                        // Use Strapi v5 relation API to clear selectedOptions and set textValue
                        await strapi.db.query("api::response.response").update({
                            where: { id: response.id },
                            data: {
                                textValue: textValue,
                                selectedOptions: {
                                    disconnect: selectedOptions.map((opt: any) => ({ id: opt.id })),
                                },
                            },
                        });

                        rolledBackCount++;
                        strapi.log.debug(`Rolled back response ${response.id}`);
                    } catch (error: any) {
                        errorCount++;
                        strapi.log.error(`Error rolling back response ${response.id}:`, error);
                    }
                }

                strapi.log.info(
                    `Rollback completed: ${rolledBackCount} responses rolled back, ${errorCount} errors`,
                );

                return {
                    total: migratedResponses.length,
                    rolledBack: rolledBackCount,
                    errors: errorCount,
                };
            } catch (error: any) {
                strapi.log.error("Rollback failed:", error);
                throw error;
            }
        },
    }),
);

/**
 * response service
 */

import { factories } from "@strapi/strapi";
import type { Core } from "@strapi/strapi";

export default factories.createCoreService(
    "api::response.response",
    ({ strapi }: { strapi: Core.Strapi }) => ({
        /**
         * Helper method to resolve option IDs from documentIds
         */
        async resolveOptionIds(optionDocumentIds: string[], questionId: number): Promise<number[]> {
            if (!Array.isArray(optionDocumentIds) || optionDocumentIds.length === 0) {
                return [];
            }

            const options = await strapi.db.query("api::option.option").findMany({
                where: {
                    documentId: { $in: optionDocumentIds },
                    question: { id: questionId }
                },
                select: ["id", "documentId"]
            });

            // Validate that all provided documentIds exist and belong to the question
            const foundDocumentIds = options.map(opt => opt.documentId);
            const missingDocumentIds = optionDocumentIds.filter(docId => !foundDocumentIds.includes(docId));

            if (missingDocumentIds.length > 0) {
                throw new Error(`Invalid option documentIds for question ${questionId}: ${missingDocumentIds.join(', ')}`);
            }

            return options.map(opt => opt.id);
        },

        /**
         * Custom method to create a response with proper relation handling
         */
        async createResponseForParticipation(data: {
            participationId: number;
            questionId: number;
            pollId: number;
            responseValue: any;
            questionType: string;
        }) {
            try {
                const { participationId, questionId, pollId, responseValue, questionType } = data;

                // Base response data
                const responseData: any = {
                    answeredAt: new Date().toISOString(),
                };

                let selectedOptionIds: number[] = [];

                // Handle different response types
                switch (questionType) {
                    case "single_choice":
                        if (typeof responseValue === "string") {
                            selectedOptionIds = await this.resolveOptionIds([responseValue], questionId);
                        }
                        break;

                    case "multiple_choice":
                        if (Array.isArray(responseValue)) {
                            selectedOptionIds = await this.resolveOptionIds(responseValue, questionId);
                        }
                        break;

                    case "rating":
                        if (
                            typeof responseValue === "number" ||
                            typeof responseValue === "string"
                        ) {
                            responseData.ratingValue = parseInt(responseValue.toString());
                        }
                        break;

                    case "text":
                        if (typeof responseValue === "string") {
                            responseData.textValue = responseValue;
                        }
                        break;

                    case "ranking":
                        if (Array.isArray(responseValue)) {
                            responseData.rankingOrder = responseValue;
                        }
                        break;
                }

                // Create response using entity service for proper relation handling
                const createdResponse = await strapi.entityService.create("api::response.response", {
                    data: {
                        ...responseData,
                        participation: participationId,
                        question: questionId,
                        poll: pollId,
                        selectedOptions: selectedOptionIds.length > 0 ? selectedOptionIds : undefined,
                    },
                });

                strapi.log.info(
                    `Response created successfully for question ${questionId}:`,
                    createdResponse.id,
                );
                return createdResponse;
            } catch (error: any) {
                strapi.log.error("Error in createResponseForParticipation:", error);
                throw new Error(`Failed to create response: ${error.message}`);
            }
        },

        /**
         * Create multiple responses for a participation
         */
        async createResponsesForParticipation(
            participationId: number,
            pollId: number,
            responses: Record<string, any>,
            questions: any[],
        ) {
            const createdResponses = [];

            for (const [questionDocumentId, responseValue] of Object.entries(responses)) {
                try {
                    // Find question by documentId
                    const question = questions.find(
                        (q: any) => q.documentId === questionDocumentId,
                    );

                    if (!question || responseValue === null || responseValue === undefined) {
                        continue;
                    }

                    const response = await this.createResponseForParticipation({
                        participationId,
                        questionId: question.id,
                        pollId,
                        responseValue,
                        questionType: question.type,
                    });

                    createdResponses.push(response);
                } catch (error: any) {
                    strapi.log.error(
                        `Failed to create response for question ${questionDocumentId}:`,
                        error,
                    );
                    // Continue with other responses
                }
            }

            return createdResponses;
        },
    }),
);

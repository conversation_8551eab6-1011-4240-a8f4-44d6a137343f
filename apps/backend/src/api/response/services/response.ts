/**
 * response service
 */

import { factories } from "@strapi/strapi";
import type { Core } from "@strapi/strapi";

export default factories.createCoreService(
    "api::response.response",
    ({ strapi }: { strapi: Core.Strapi }) => ({
        /**
         * Custom method to create a response with proper relation handling
         */
        async createResponseForParticipation(data: {
            participationId: number;
            questionId: number;
            pollId: number;
            responseValue: any;
            questionType: string;
        }) {
            try {
                const { participationId, questionId, pollId, responseValue, questionType } = data;

                // Base response data
                const responseData: any = {
                    answeredAt: new Date().toISOString(),
                };

                // Handle different response types
                switch (questionType) {
                    case "single_choice":
                    case "multiple_choice":
                        // For now, store options as text to avoid relation complexity
                        if (questionType === "single_choice" && typeof responseValue === "string") {
                            responseData.textValue = `option:${responseValue}`;
                        } else if (
                            questionType === "multiple_choice" &&
                            Array.isArray(responseValue)
                        ) {
                            responseData.textValue = `options:${JSON.stringify(responseValue)}`;
                        }
                        break;

                    case "rating":
                        if (
                            typeof responseValue === "number" ||
                            typeof responseValue === "string"
                        ) {
                            responseData.ratingValue = parseInt(responseValue.toString());
                        }
                        break;

                    case "text":
                        if (typeof responseValue === "string") {
                            responseData.textValue = responseValue;
                        }
                        break;

                    case "ranking":
                        if (Array.isArray(responseValue)) {
                            responseData.rankingOrder = responseValue;
                        }
                        break;
                }

                // Create response using direct database query to avoid entity service issues
                const createdResponse = await strapi.db.query("api::response.response").create({
                    data: {
                        ...responseData,
                        participation: participationId,
                        question: questionId,
                        poll: pollId,
                    },
                });

                strapi.log.info(
                    `Response created successfully for question ${questionId}:`,
                    createdResponse.id,
                );
                return createdResponse;
            } catch (error: any) {
                strapi.log.error("Error in createResponseForParticipation:", error);
                throw new Error(`Failed to create response: ${error.message}`);
            }
        },

        /**
         * Create multiple responses for a participation
         */
        async createResponsesForParticipation(
            participationId: number,
            pollId: number,
            responses: Record<string, any>,
            questions: any[],
        ) {
            const createdResponses = [];

            for (const [questionDocumentId, responseValue] of Object.entries(responses)) {
                try {
                    // Find question by documentId
                    const question = questions.find(
                        (q: any) => q.documentId === questionDocumentId,
                    );

                    if (!question || responseValue === null || responseValue === undefined) {
                        continue;
                    }

                    const response = await this.createResponseForParticipation({
                        participationId,
                        questionId: question.id,
                        pollId,
                        responseValue,
                        questionType: question.type,
                    });

                    createdResponses.push(response);
                } catch (error: any) {
                    strapi.log.error(
                        `Failed to create response for question ${questionDocumentId}:`,
                        error,
                    );
                    // Continue with other responses
                }
            }

            return createdResponses;
        },
    }),
);

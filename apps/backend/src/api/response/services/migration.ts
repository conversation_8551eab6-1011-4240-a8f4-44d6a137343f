/**
 * Migration utility for converting legacy textValue responses to selectedOptions relations
 */

import type { Core } from "@strapi/strapi";

export default {
    /**
     * Migrate legacy textValue responses to selectedOptions relations
     * This should be run once after deploying the new response handling code
     */
    async migrateLegacyResponses(strapi: Core.Strapi) {
        try {
            strapi.log.info("Starting migration of legacy responses...");

            // Find all responses with textValue that start with "option:" or "options:"
            const legacyResponses = await strapi.db.query("api::response.response").findMany({
                where: {
                    textValue: { $notNull: true },
                    selectedOptions: { $null: true }, // Only migrate if selectedOptions is not set
                    $or: [
                        { textValue: { $startsWith: "option:" } },
                        { textValue: { $startsWith: "options:" } }
                    ]
                },
                populate: {
                    question: {
                        populate: ["options"]
                    }
                }
            });

            strapi.log.info(`Found ${legacyResponses.length} legacy responses to migrate`);

            let migratedCount = 0;
            let errorCount = 0;

            for (const response of legacyResponses) {
                try {
                    const question = response.question;
                    if (!question || !question.options) {
                        strapi.log.warn(`Skipping response ${response.id}: question or options not found`);
                        continue;
                    }

                    let optionIds: number[] = [];

                    if (response.textValue.startsWith("option:")) {
                        // Single choice: "option:documentId"
                        const optionDocumentId = response.textValue.substring(7);
                        const option = question.options.find((opt: any) => opt.documentId === optionDocumentId);
                        if (option) {
                            optionIds = [option.id];
                        } else {
                            strapi.log.warn(`Option with documentId ${optionDocumentId} not found for response ${response.id}`);
                            continue;
                        }
                    } else if (response.textValue.startsWith("options:")) {
                        // Multiple choice: "options:[\"documentId1\",\"documentId2\"]"
                        try {
                            const optionDocumentIds = JSON.parse(response.textValue.substring(8));
                            if (Array.isArray(optionDocumentIds)) {
                                optionIds = [];
                                for (const docId of optionDocumentIds) {
                                    const option = question.options.find((opt: any) => opt.documentId === docId);
                                    if (option) {
                                        optionIds.push(option.id);
                                    } else {
                                        strapi.log.warn(`Option with documentId ${docId} not found for response ${response.id}`);
                                    }
                                }
                            }
                        } catch (parseError) {
                            strapi.log.warn(`Failed to parse options JSON for response ${response.id}: ${response.textValue}`);
                            continue;
                        }
                    }

                    if (optionIds.length > 0) {
                        // Update the response with selectedOptions and clear textValue
                        await strapi.entityService.update("api::response.response", response.id, {
                            data: {
                                selectedOptions: optionIds,
                                textValue: null // Clear the legacy textValue
                            }
                        });

                        migratedCount++;
                        strapi.log.debug(`Migrated response ${response.id} with ${optionIds.length} options`);
                    }

                } catch (error: any) {
                    errorCount++;
                    strapi.log.error(`Error migrating response ${response.id}:`, error);
                }
            }

            strapi.log.info(`Migration completed: ${migratedCount} responses migrated, ${errorCount} errors`);
            
            return {
                total: legacyResponses.length,
                migrated: migratedCount,
                errors: errorCount
            };

        } catch (error: any) {
            strapi.log.error("Migration failed:", error);
            throw error;
        }
    },

    /**
     * Rollback migration - convert selectedOptions back to textValue format
     * Use this if you need to rollback the migration
     */
    async rollbackMigration(strapi: Core.Strapi) {
        try {
            strapi.log.info("Starting rollback of migrated responses...");

            // Find responses with selectedOptions but no textValue
            const migratedResponses = await strapi.db.query("api::response.response").findMany({
                where: {
                    selectedOptions: { $notNull: true },
                    textValue: { $null: true }
                },
                populate: {
                    selectedOptions: true,
                    question: true
                }
            });

            strapi.log.info(`Found ${migratedResponses.length} migrated responses to rollback`);

            let rolledBackCount = 0;
            let errorCount = 0;

            for (const response of migratedResponses) {
                try {
                    const question = response.question;
                    const selectedOptions = response.selectedOptions;

                    if (!question || !selectedOptions || selectedOptions.length === 0) {
                        continue;
                    }

                    let textValue: string;

                    if (question.type === "single_choice" && selectedOptions.length === 1) {
                        textValue = `option:${selectedOptions[0].documentId}`;
                    } else if (question.type === "multiple_choice") {
                        const documentIds = selectedOptions.map((opt: any) => opt.documentId);
                        textValue = `options:${JSON.stringify(documentIds)}`;
                    } else {
                        // Skip responses that don't match expected patterns
                        continue;
                    }

                    // Update the response with textValue and clear selectedOptions
                    await strapi.entityService.update("api::response.response", response.id, {
                        data: {
                            textValue: textValue,
                            selectedOptions: null
                        }
                    });

                    rolledBackCount++;
                    strapi.log.debug(`Rolled back response ${response.id}`);

                } catch (error: any) {
                    errorCount++;
                    strapi.log.error(`Error rolling back response ${response.id}:`, error);
                }
            }

            strapi.log.info(`Rollback completed: ${rolledBackCount} responses rolled back, ${errorCount} errors`);
            
            return {
                total: migratedResponses.length,
                rolledBack: rolledBackCount,
                errors: errorCount
            };

        } catch (error: any) {
            strapi.log.error("Rollback failed:", error);
            throw error;
        }
    }
};

/**
 * response router
 */

import { factories } from "@strapi/strapi";

export default {
    ...factories.createCoreRouter("api::response.response"),
    routes: [
        ...factories.createCoreRouter("api::response.response").routes,
        {
            method: "POST",
            path: "/responses/migrate-legacy",
            handler: "response.migrateLegacy",
            config: {
                auth: {
                    scope: ["admin"]
                }
            }
        },
        {
            method: "POST",
            path: "/responses/rollback-migration",
            handler: "response.rollbackMigration",
            config: {
                auth: {
                    scope: ["admin"]
                }
            }
        }
    ]
};

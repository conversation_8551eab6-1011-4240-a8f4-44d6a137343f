/**
 * response router
 */

import { factories } from "@strapi/strapi";

const coreRouter = factories.createCoreRouter("api::response.response");

export default {
    routes: [
        ...(typeof coreRouter.routes === 'function' ? coreRouter.routes() : coreRouter.routes),
        {
            method: "POST",
            path: "/responses/migrate-legacy",
            handler: "response.migrateLegacy",
            config: {
                auth: {
                    scope: ["admin"]
                }
            }
        },
        {
            method: "POST",
            path: "/responses/rollback-migration",
            handler: "response.rollbackMigration",
            config: {
                auth: {
                    scope: ["admin"]
                }
            }
        }
    ]
};
